#include "sourceFlow/flowSolver/StructuralDynamics.h"
#include "basic/common/CommonInclude.h"
#include <fstream>
#include <sstream>

StructuralDynamics::StructuralDynamics()
    : numModes(0)
    , newmarkBeta(0.25)
    , newmarkGamma(0.5)
    , currentTime(0.0)
    , isInitialized(false)
{
}

StructuralDynamics::~StructuralDynamics()
{
}

void StructuralDynamics::Initialize(const std::string& modalDataFile)
{
    if (GetMPIRank() == 0) {
        Print("初始化结构动力学系统...");
    }
    
    // 读取模态数据
    ReadModalData(modalDataFile);
    
    // 构建广义矩阵
    BuildGeneralizedMatrices();
    
    // 初始化状态向量
    generalizedDisp.resize(numModes, 0.0);
    generalizedVel.resize(numModes, 0.0);
    generalizedAcc.resize(numModes, 0.0);
    
    generalizedDispOld.resize(numModes, 0.0);
    generalizedVelOld.resize(numModes, 0.0);
    generalizedAccOld.resize(numModes, 0.0);
    
    isInitialized = true;
    
    if (GetMPIRank() == 0) {
        Print("结构动力学系统初始化完成，模态数量: " + ToString(numModes));
    }
}

void StructuralDynamics::ReadModalData(const std::string& modalDataFile)
{
    if (GetMPIRank() == 0) {
        Print("读取模态数据文件: " + modalDataFile);
    }
    
    std::ifstream file(modalDataFile);
    if (!file.is_open()) {
        FatalError("无法打开模态数据文件: " + modalDataFile);
    }
    
    std::string line;
    
    // 读取模态数量
    if (std::getline(file, line)) {
        std::istringstream iss(line);
        iss >> numModes;
    }
    
    if (numModes <= 0) {
        FatalError("无效的模态数量: " + ToString(numModes));
    }
    
    // 初始化数据结构
    naturalFrequencies.resize(numModes);
    dampingRatios.resize(numModes, 0.02); // 默认阻尼比2%
    modeShapeData.resize(numModes);
    
    // 读取各模态的固有频率和振型数据
    for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
        // 读取固有频率
        if (std::getline(file, line)) {
            std::istringstream iss(line);
            iss >> naturalFrequencies[modeIdx];
        }
        
        // 读取节点数量
        int numNodes;
        if (std::getline(file, line)) {
            std::istringstream iss(line);
            iss >> numNodes;
        }
        
        // 读取节点坐标和模态振型
        modeShapeData[modeIdx].nodeCoordinates.resize(numNodes);
        modeShapeData[modeIdx].modeShapes.resize(numNodes);
        
        for (int nodeIdx = 0; nodeIdx < numNodes; nodeIdx++) {
            if (std::getline(file, line)) {
                std::istringstream iss(line);
                double x, y, z, dx, dy, dz;
                iss >> x >> y >> z >> dx >> dy >> dz;
                
                modeShapeData[modeIdx].nodeCoordinates[nodeIdx] = Vector(x, y, z);
                modeShapeData[modeIdx].modeShapes[nodeIdx] = Vector(dx, dy, dz);
            }
        }
    }
    
    file.close();
    
    if (GetMPIRank() == 0) {
        Print("模态数据读取完成");
        for (int i = 0; i < numModes; i++) {
            Print("模态 " + ToString(i+1) + ": 频率 = " + ToString(naturalFrequencies[i]) + " rad/s");
        }
    }
}

void StructuralDynamics::BuildGeneralizedMatrices()
{
    // 初始化广义矩阵
    generalizedMass.resize(numModes);
    generalizedStiff.resize(numModes);
    generalizedDamp.resize(numModes);
    
    // 对于正交化的模态，广义质量矩阵为单位矩阵，广义刚度矩阵为对角矩阵
    for (int i = 0; i < numModes; i++) {
        generalizedMass[i] = 1.0;  // 归一化的广义质量
        generalizedStiff[i] = naturalFrequencies[i] * naturalFrequencies[i];  // ω²
        generalizedDamp[i] = 2.0 * dampingRatios[i] * naturalFrequencies[i];  // 2ζω
    }
}

void StructuralDynamics::SolveGeneralizedEquation(const std::vector<double>& generalizedForces, double timeStep)
{
    if (!isInitialized) {
        FatalError("结构动力学系统未初始化");
    }
    
    if (generalizedForces.size() != numModes) {
        FatalError("广义力向量维度不匹配");
    }
    
    // 保存上一时间步的状态
    generalizedDispOld = generalizedDisp;
    generalizedVelOld = generalizedVel;
    generalizedAccOld = generalizedAcc;
    
    // Newmark-β方法求解每个模态的运动方程
    // 对于解耦的模态方程：m*q̈ + c*q̇ + k*q = Q
    
    double dt = timeStep;
    double beta = newmarkBeta;
    double gamma = newmarkGamma;
    
    for (int i = 0; i < numModes; i++) {
        double m = generalizedMass[i];
        double c = generalizedDamp[i];
        double k = generalizedStiff[i];
        double Q = generalizedForces[i];
        
        // Newmark-β预测
        double qPred = generalizedDispOld[i] + dt * generalizedVelOld[i] + 
                       0.5 * dt * dt * (1.0 - 2.0 * beta) * generalizedAccOld[i];
        double vPred = generalizedVelOld[i] + dt * (1.0 - gamma) * generalizedAccOld[i];
        
        // 有效刚度矩阵
        double kEff = k + gamma / (beta * dt) * c + 1.0 / (beta * dt * dt) * m;
        
        // 有效载荷
        double QEff = Q + m * (1.0 / (beta * dt * dt) * qPred + 1.0 / (beta * dt) * vPred) +
                      c * (gamma / (beta * dt) * qPred + (gamma / beta - 1.0) * vPred);
        
        // 求解位移
        generalizedDisp[i] = QEff / kEff;
        
        // 修正速度和加速度
        generalizedAcc[i] = 1.0 / (beta * dt * dt) * (generalizedDisp[i] - qPred);
        generalizedVel[i] = vPred + gamma * dt * generalizedAcc[i];
    }
    
    currentTime += timeStep;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedDisplacement() const
{
    return generalizedDisp;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedVelocity() const
{
    return generalizedVel;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedAcceleration() const
{
    return generalizedAcc;
}

int StructuralDynamics::GetNumberOfModes() const
{
    return numModes;
}

Vector StructuralDynamics::GetModeShapeAtPoint(int modeIndex, const Vector& point) const
{
    if (modeIndex < 0 || modeIndex >= numModes) {
        FatalError("模态索引超出范围: " + ToString(modeIndex));
    }
    
    return InterpolateModeShape(modeIndex, point);
}

Vector StructuralDynamics::InterpolateModeShape(int modeIndex, const Vector& point) const
{
    // 简单的最近邻插值（实际应用中可以使用更复杂的插值方法）
    const auto& nodes = modeShapeData[modeIndex].nodeCoordinates;
    const auto& shapes = modeShapeData[modeIndex].modeShapes;
    
    if (nodes.empty()) {
        return Vector0;
    }
    
    // 找到最近的节点
    int nearestIdx = 0;
    double minDist = (point - nodes[0]).GetMagnitude();
    
    for (int i = 1; i < nodes.size(); i++) {
        double dist = (point - nodes[i]).GetMagnitude();
        if (dist < minDist) {
            minDist = dist;
            nearestIdx = i;
        }
    }
    
    return shapes[nearestIdx];
}

void StructuralDynamics::SetDampingRatio(const std::vector<double>& dampingRatio)
{
    if (dampingRatio.size() != numModes) {
        FatalError("阻尼比向量维度不匹配");
    }
    
    dampingRatios = dampingRatio;
    
    // 重新构建阻尼矩阵
    for (int i = 0; i < numModes; i++) {
        generalizedDamp[i] = 2.0 * dampingRatios[i] * naturalFrequencies[i];
    }
}

void StructuralDynamics::SetNewmarkParameters(double beta, double gamma)
{
    newmarkBeta = beta;
    newmarkGamma = gamma;
}

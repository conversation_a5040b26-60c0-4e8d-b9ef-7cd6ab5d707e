#include "sourceFlow/flowSolver/StructuralDynamics.h"
#include "basic/common/CommonInclude.h"
#include <fstream>
#include <sstream>

StructuralDynamics::StructuralDynamics()
    : numModes(0)
    , newmarkBeta(0.25)
    , newmarkGamma(0.5)
    , currentTime(0.0)
    , isInitialized(false)
{
}

StructuralDynamics::~StructuralDynamics()
{
}

void StructuralDynamics::Initialize(const std::vector<ElementField<Vector>*>& wallPerturbationFields,
                                   const std::vector<double>& frequencies)
{
    if (GetMPIRank() == 0) {
        Print("初始化结构动力学系统...");
    }

    // 设置模态数量和频率
    numModes = wallPerturbationFields.size();
    if (frequencies.size() != numModes) {
        FatalError("频率向量维度与物面摄动场数量不匹配");
    }

    naturalFrequencies = frequencies;

    // 初始化阻尼比（默认值）
    dampingRatios.resize(numModes, 0.02); // 默认2%阻尼

    // 构建广义矩阵
    BuildGeneralizedMatrices();

    // 初始化状态向量
    generalizedDisp.resize(numModes, 0.0);
    generalizedVel.resize(numModes, 0.0);
    generalizedAcc.resize(numModes, 0.0);

    generalizedDispOld.resize(numModes, 0.0);
    generalizedVelOld.resize(numModes, 0.0);
    generalizedAccOld.resize(numModes, 0.0);

    isInitialized = true;

    if (GetMPIRank() == 0) {
        Print("结构动力学系统初始化完成，模态数量: " + ToString(numModes));
        for (int i = 0; i < numModes; i++) {
            Print("模态 " + ToString(i+1) + ": 频率 = " + ToString(naturalFrequencies[i]) + " rad/s");
        }
    }
}



void StructuralDynamics::BuildGeneralizedMatrices()
{
    // 初始化广义矩阵
    generalizedMass.resize(numModes);
    generalizedStiff.resize(numModes);
    generalizedDamp.resize(numModes);
    
    // 对于正交化的模态，广义质量矩阵为单位矩阵，广义刚度矩阵为对角矩阵
    for (int i = 0; i < numModes; i++) {
        generalizedMass[i] = 1.0;  // 归一化的广义质量
        generalizedStiff[i] = naturalFrequencies[i] * naturalFrequencies[i];  // ω²
        generalizedDamp[i] = 2.0 * dampingRatios[i] * naturalFrequencies[i];  // 2ζω
    }
}

void StructuralDynamics::SolveGeneralizedEquation(const std::vector<double>& generalizedForces, double timeStep)
{
    if (!isInitialized) {
        FatalError("结构动力学系统未初始化");
    }
    
    if (generalizedForces.size() != numModes) {
        FatalError("广义力向量维度不匹配");
    }
    
    // 保存上一时间步的状态
    generalizedDispOld = generalizedDisp;
    generalizedVelOld = generalizedVel;
    generalizedAccOld = generalizedAcc;
    
    // Newmark-β方法求解每个模态的运动方程
    // 对于解耦的模态方程：m*q̈ + c*q̇ + k*q = Q
    
    double dt = timeStep;
    double beta = newmarkBeta;
    double gamma = newmarkGamma;
    
    for (int i = 0; i < numModes; i++) {
        double m = generalizedMass[i];
        double c = generalizedDamp[i];
        double k = generalizedStiff[i];
        double Q = generalizedForces[i];
        
        // Newmark-β预测
        double qPred = generalizedDispOld[i] + dt * generalizedVelOld[i] + 
                       0.5 * dt * dt * (1.0 - 2.0 * beta) * generalizedAccOld[i];
        double vPred = generalizedVelOld[i] + dt * (1.0 - gamma) * generalizedAccOld[i];
        
        // 有效刚度矩阵
        double kEff = k + gamma / (beta * dt) * c + 1.0 / (beta * dt * dt) * m;
        
        // 有效载荷
        double QEff = Q + m * (1.0 / (beta * dt * dt) * qPred + 1.0 / (beta * dt) * vPred) +
                      c * (gamma / (beta * dt) * qPred + (gamma / beta - 1.0) * vPred);
        
        // 求解位移
        generalizedDisp[i] = QEff / kEff;
        
        // 修正速度和加速度
        generalizedAcc[i] = 1.0 / (beta * dt * dt) * (generalizedDisp[i] - qPred);
        generalizedVel[i] = vPred + gamma * dt * generalizedAcc[i];
    }
    
    currentTime += timeStep;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedDisplacement() const
{
    return generalizedDisp;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedVelocity() const
{
    return generalizedVel;
}

const std::vector<double>& StructuralDynamics::GetGeneralizedAcceleration() const
{
    return generalizedAcc;
}

int StructuralDynamics::GetNumberOfModes() const
{
    return numModes;
}

Vector StructuralDynamics::GetModeShapeAtPoint(int modeIndex, const Vector& point) const
{
    // 此方法已废弃，模态振型数据现在直接从摄动场中获取
    FatalError("GetModeShapeAtPoint方法已废弃，请直接使用摄动场数据");
    return Vector0;
}

void StructuralDynamics::SetDampingRatio(const std::vector<double>& dampingRatio)
{
    if (dampingRatio.size() != numModes) {
        FatalError("阻尼比向量维度不匹配");
    }
    
    dampingRatios = dampingRatio;
    
    // 重新构建阻尼矩阵
    for (int i = 0; i < numModes; i++) {
        generalizedDamp[i] = 2.0 * dampingRatios[i] * naturalFrequencies[i];
    }
}

void StructuralDynamics::SetNewmarkParameters(double beta, double gamma)
{
    newmarkBeta = beta;
    newmarkGamma = gamma;
}

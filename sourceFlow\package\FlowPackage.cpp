#include "sourceFlow/package/FlowPackage.h"

namespace Package
{

FlowPackage::FlowPackage(const int &level, SubMesh *subMesh, Configure::Flow::FlowConfigure &flowConfigure_)
    :
    DataPackage(subMesh->GetMultiGrid(level)), flowConfigure(flowConfigure_),
    material(flowConfigure_.GetMaterial().density, flowConfigure_.GetMaterial().viscosity, flowConfigure_.GetMaterial().cpType)
{
	// 网格信息
    meshStruct.mesh = subMesh->GetMultiGrid(level);
    meshStruct.level = level;
    meshStruct.processorID = GetMPIRank();
	if (meshStruct.mesh->GetMeshDimension() == Mesh::MeshDim::md2D)
	{
		meshStruct.dim2 = true;
		meshStruct.dim3 = false;
	}
	else
	{
		meshStruct.dim2 = false;
		meshStruct.dim3 = true;
	}

	//材料信息
	materialNumber.gamma = material.GAMMA();
	materialNumber.Cp = material.CP();
	materialNumber.Cv = material.CV();
	materialNumber.R = material.R();
	materialNumber.gamma1 = materialNumber.gamma - 1.0;
	materialNumber.PrandtlLaminar = flowConfigure.GetModel().PrandtlLaminar;
	materialNumber.PrandtlTurbulent = flowConfigure.GetModel().PrandtlTurbulent;
	materialNumber.stokes = -2.0/3.0;

	//建立场指针及开辟空间（注意调用顺序）
    this->SetTurbulentStatus();
#if defined(_EnableMultiSpecies_)
	this->SetMultiSpeciesStatus();
#endif
    this->SetUnsteadyStatus();
    this->SetFieldPointer();

	//分配用于计算使用的临时场（初始为8个标量场，4个矢量场，不够时可动态调整）
    const int nScalarTempField = 12;
    const int nVectorTempField = 4;
    this->SetTempField(nScalarTempField, nVectorTempField);

	//预计算扩散项计算需要的几何信息
	this->PreCalculate();

	//MRF初始化
	auto& mrftmp = GetFlowConfigure().GetMRF();
	if (mrftmp.MRFFlag)
	{
		mrf = std::make_shared<MRFZONE>(mrftmp.MRFName, mrftmp.MRForigin, mrftmp.MRFaxis, mrftmp.MRFomega,meshStruct.mesh);
	}

    // 边界类型
    const int flowBoundarySize = this->GetMeshStruct().mesh->GetBoundarySize();
    for (int i = 0; i < flowBoundarySize; ++i)
        flowBoundaryType.push_back(flowConfigure.GetLocalBoundary(level, i).type);

	//创建并获取多域信息
	if (level == 0 )
	{
		zoneManager = new ZoneManager(meshStruct.mesh);
		zoneManager->PrintZoneInfo(GetMPIRank());
	}

}

FlowPackage::~FlowPackage()
{
    // 创建上一步物理场备份，用于多重网格延拓
    if(meshStruct.level > 0)
    {
		if (field0PointerBackup.density != nullptr)
		{
			delete field0PointerBackup.density;
			field0PointerBackup.density = nullptr;
		}
		if (field0PointerBackup.velocity != nullptr)
		{
			delete field0PointerBackup.velocity;
			field0PointerBackup.velocity = nullptr;
		}

		if (field0PointerBackup.pressure != nullptr)
		{
			delete field0PointerBackup.pressure;
			field0PointerBackup.pressure = nullptr;
		}
	    for (int k = 0; k < turbulentStatus.nVariable; ++k)
		{
			if (field0PointerBackup.turbulence[k] != nullptr)
			{
				delete field0PointerBackup.turbulence[k];
				field0PointerBackup.turbulence[k] = nullptr;
			}
		}
    }

    const int fieldSize = (int)unsteadyFieldPointer.size();
    for (int i = 1; i < fieldSize; ++i)
    {
		if (unsteadyFieldPointer[i].density != nullptr)
		{
			delete unsteadyFieldPointer[i].density;
			unsteadyFieldPointer[i].density = nullptr;
		}
		if (unsteadyFieldPointer[i].velocity != nullptr)
		{
			delete unsteadyFieldPointer[i].velocity;
			unsteadyFieldPointer[i].velocity = nullptr;
		}
		if (unsteadyFieldPointer[i].pressure != nullptr)
		{
			delete unsteadyFieldPointer[i].pressure;
			unsteadyFieldPointer[i].pressure = nullptr;
		}
	    for (int k = 0; k < turbulentStatus.nVariable; ++k)
		{
			if (unsteadyFieldPointer[i].turbulence[k] != nullptr)
			{
				delete unsteadyFieldPointer[i].turbulence[k];
				unsteadyFieldPointer[i].turbulence[k] = nullptr;
			}
		}
    }

	for (int m = 0; m < turbulentStatus.nVariable; ++m)
	{
		if (fieldPointer.jacobianTurbulence[m] != nullptr)
		{
			delete fieldPointer.jacobianTurbulence[m];
			fieldPointer.jacobianTurbulence[m] = nullptr;
		}
	}

	if (turbulentStatus.freeStreamValue != nullptr)
	{
		delete turbulentStatus.freeStreamValue;
		turbulentStatus.freeStreamValue = nullptr;
	}

	if (implicitSolver.jacobian != nullptr)
	{
		delete implicitSolver.jacobian;
		implicitSolver.jacobian = nullptr;
	}
	if (implicitSolver.jacobianTur != nullptr)
	{
		delete implicitSolver.jacobianTur;
		implicitSolver.jacobianTur = nullptr;
	}
}

void FlowPackage::SetTurbulentStatus()
{
	turbulentStatus.viscousFlag = false;
	turbulentStatus.laminarFlag = false;
	turbulentStatus.turbulenceFlag = false;
	turbulentStatus.desFlag = false;
    turbulentStatus.nVariable = 0;
    turbulentStatus.variableMacro.clear();
    turbulentStatus.variableMacro0.clear();
    turbulentStatus.variableMacro00.clear();
    turbulentStatus.residualMacro.clear();
    turbulentStatus.gradientMacro.clear();
    
	fieldPointer.lambdaViscous = nullptr;
	fieldPointer.muLaminar = nullptr;
    fieldPointer.muTurbulent = nullptr;
    fieldPointer.alphaT = nullptr;
    fieldPointer.muTurbulentRANS = nullptr;
	fieldPointer.ldOverlr = nullptr;
	fieldPointer.shieldingFunction = nullptr;

	fieldPointer.jacobianTurbulence.clear();
    turbulentStatus.turbulenceType = flowConfigure.GetModel().type;
    turbulentStatus.freeStreamValue = nullptr;
    if (turbulentStatus.turbulenceType != Turbulence::Model::INVISCID)
    {
		turbulentStatus.viscousFlag = true;
		fieldPointer.muLaminar = this->CreatScalarElementField(FlowMacro::Scalar::MU_LAMINAR, "MU_LAMINAR");
		fieldPointer.lambdaViscous = this->CreatScalarElementField(FlowMacro::Scalar::LAMBDA_VISCOUS, "LAMBDA_VISCOUS");

		if (turbulentStatus.turbulenceType == Turbulence::Model::LAMINAR)
		{			
			turbulentStatus.laminarFlag = true;
		}
		else
		{
			turbulentStatus.turbulenceFlag = true;
			fieldPointer.muTurbulent = this->CreatScalarElementField(FlowMacro::Scalar::MU_TURBULENT, "MU_TURBULENT");

			if(turbulentStatus.turbulenceType == Turbulence::Model::K_OMEGA_V2)
			    fieldPointer.alphaT = this->CreatScalarElementField(FlowMacro::Scalar::ALPHAT, "ALPHAT");
			
			if(turbulentStatus.turbulenceType == Turbulence::Model::MENTER_SST_ML)
			    fieldPointer.muTurbulentRANS = this->CreatScalarElementField(FlowMacro::Scalar::MU_TURBULENT_RANS, "MU_TURBULENT_RANS");

			const auto &turbulentType = flowConfigure.GetModel().type;
			turbulentStatus.variableMacro = Configure::Flow::turbulenceVariableMap.find(turbulentType)->second;
			turbulentStatus.variableMacro0 = Configure::Flow::turbulenceVariable0Map.find(turbulentType)->second;
			turbulentStatus.variableMacro00 = Configure::Flow::turbulenceVariable00Map.find(turbulentType)->second;
			turbulentStatus.residualMacro = Configure::Flow::turbulenceResidualMap.find(turbulentType)->second;
			turbulentStatus.gradientMacro = Configure::Flow::turbulenceGradientMap.find(turbulentType)->second;
			turbulentStatus.nVariable = turbulentStatus.variableMacro.size();
            if (!flowConfigure.GetAcceleration().multigridSolver.coarseMeshTurbulenceFlag
				&& meshStruct.level > 0) turbulentStatus.nVariable = 0;

			for (int m = 0; m < turbulentStatus.nVariable; ++m)
				fieldPointer.jacobianTurbulence.push_back(new ElementField<Scalar>(meshStruct.mesh, Scalar0, "jacobianTurbulence" + ToString(m)));

            turbulentStatus.freeStreamValue = new std::vector<Scalar>();

            if (turbulentStatus.turbulenceType == Turbulence::Model::SPALART_ALLMARAS_DES || turbulentStatus.turbulenceType == Turbulence::Model::MENTER_SST_DES ||
                turbulentStatus.turbulenceType == Turbulence::Model::SPALART_ALLMARAS_DDES || turbulentStatus.turbulenceType == Turbulence::Model::MENTER_SST_DDES)
            {
            	turbulentStatus.desFlag = true;
            	fieldPointer.ldOverlr = this->CreatScalarElementField(FlowMacro::Scalar::LDOVERLR, "LDOVERLR");

				if (turbulentStatus.turbulenceType == Turbulence::Model::SPALART_ALLMARAS_DDES || turbulentStatus.turbulenceType == Turbulence::Model::MENTER_SST_DDES)
					fieldPointer.shieldingFunction = this->CreatScalarElementField(FlowMacro::Scalar::SHIELDINGFUNCTION, "SHIELDINGFUNCTION");
            }
        }
	}
}

void FlowPackage::SetUnsteadyStatus()
{
    const auto &unsteadyType = flowConfigure.GetTimeScheme().outerLoopType;
    if (unsteadyType == Time::UnsteadyType::STEADY)
    {
        unsteadyStatus.unsteadyFlag = false;
        unsteadyStatus.dualTime = false;
    }
    else if (unsteadyType == Time::UnsteadyType::DUAL_TIME_STEP)
    {
        unsteadyStatus.unsteadyFlag = true;
        unsteadyStatus.dualTime = true;
    }
    else if (unsteadyType == Time::UnsteadyType::GLOBAL_TIME_STEP)
    {
        unsteadyStatus.unsteadyFlag = true;
        unsteadyStatus.dualTime = false;
    }
    else
    {
        FatalError("FlowPackage::SetUnsteadyStatus: unsteady time type isnot supported!");
		return;
    }
    
    unsteadyStatus.unsteadyOrder = flowConfigure.GetTimeScheme().unsteadyTimeOrder;
    unsteadyStatus.timeStep = flowConfigure.GetControl().outerLoop.timeStep;
    unsteadyStatus.totalTime = flowConfigure.GetControl().outerLoop.totalTime;
    unsteadyStatus.currentTime = 0.0;

	fieldPointer.meanMuTurbulent = nullptr;
	fieldPointer.meanMu = nullptr;
	fieldPointer.meanPressure = nullptr;
	fieldPointer.meanVelocity = nullptr;
	if (flowConfigure.GetTimeScheme().outerLoopType != Time::UnsteadyType::STEADY)
	{
		if (turbulentStatus.turbulenceFlag) 
			fieldPointer.meanMuTurbulent = this->CreatScalarElementField(FlowMacro::Scalar::MEANMUTURBULENT, "MEANMUTURBULENT");
		fieldPointer.meanMu = this->CreatScalarElementField(FlowMacro::Scalar::MEANMU, "MEANMU");
		fieldPointer.meanPressure = this->CreatScalarElementField(FlowMacro::Scalar::MEANPRESSURE, "MEANPRESSURE");
		fieldPointer.meanDensity = this->CreatScalarElementField(FlowMacro::Scalar::MEANPRESSURE, "MEANDENSITY");
		fieldPointer.meanVelocity = this->CreatVectorElementField(FlowMacro::Vector::MEANU, "MEANU");
	}
}

#if defined(_EnableMultiSpecies_)
void FlowPackage::SetMultiSpeciesStatus()
{
	if (!flowConfigure.GetModel().multiSpeciesFlag)
	{
		multiSpeciesStatus.speciesSize = 0;
		multiSpeciesStatus.reactionSize = 0;
		return;
	}
	
	material.SetMultiSpecies();
	
	const int &speciesSize = material.GetSpeciesSize();
	const int &reactionSize = material.GetReactionSize();

	multiSpeciesStatus.speciesSize = speciesSize;
	multiSpeciesStatus.reactionSize = reactionSize;

	multiSpeciesStatus.speciesList.resize(speciesSize);
	multiSpeciesStatus.massFractionR.resize(speciesSize);
	multiSpeciesStatus.massFractionP.resize(speciesSize);

	for (int k = 0; k < speciesSize; ++k)
	{
		multiSpeciesStatus.speciesList[k] = material.GetPureSpeciesData(k).speciesName;
		multiSpeciesStatus.massFractionR[k] = flowConfigure.GetModel().multiSpecies.massFractionR[k];
		multiSpeciesStatus.massFractionP[k] = flowConfigure.GetModel().multiSpecies.massFractionP[k];
	}
}
#endif

void FlowPackage::SetFieldPointer()
{
	// 在新定义的物理场包中，创建基本变量场
	fieldPointer.density     = this->CreatScalarElementField(FlowMacro::Scalar::RHO, "RHO");
	fieldPointer.velocity    = this->CreatVectorElementField(FlowMacro::Vector::U, "U");
	fieldPointer.pressure    = this->CreatScalarElementField(FlowMacro::Scalar::P, "P");
	fieldPointer.temperature = this->CreatScalarElementField(FlowMacro::Scalar::T, "T");
	fieldPointer.enthalpy    = this->CreatScalarElementField(FlowMacro::Scalar::H, "H");
    fieldPointer.soundSpeed = this->CreatScalarElementField(FlowMacro::Scalar::A, "A");
	fieldPointer.lambdaConvective = this->CreatScalarElementField(FlowMacro::Scalar::LAMBDA_CONVECTIVE, "LAMBDA_CONVECTIVE");
	fieldPointer.deltaT = this->CreatScalarElementField(FlowMacro::Scalar::DELTA_T, "DELTA_T");
    fieldPointer.pressureSwitch = this->CreatScalarElementField(FlowMacro::Scalar::PRESSURE_SWITCH, "PRESSURE_SWITCH");
	
	for (int k = 0; k < turbulentStatus.nVariable; ++k)
	{
		const auto &marco = turbulentStatus.variableMacro[k];
		const std::string &name = Configure::Flow::turbulenceVariableNameReverseMap.find(marco)->second;
		fieldPointer.turbulence.push_back(this->CreatScalarElementField(marco, name));
	}

#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
	{
		const std::string &name = multiSpeciesStatus.speciesList[k];
		fieldPointer.massFraction.push_back(this->CreatScalarElementField(FlowMacro::Scalar::MASSFRACTION, name));
	}
#endif

#if defined(_EnableMLTurbModel_)
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::STENSOR,"STENSOR"));
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::WTENSOR,"WTENSOR"));
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::VORTEX,"VORTEX"));
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::FD,"FD"));
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::TKOVERK,"TKOVERK"));
	fieldPointer.features.push_back(this->CreatScalarElementField(FlowMacro::Scalar::CHI,"CHI"));
#endif

    // 在新定义的物理场包中，创建上一步物理场
	field0Pointer.density =  this->CreatScalarElementField(FlowMacro::Scalar::RHO0, "RHO0");
	field0Pointer.velocity = this->CreatVectorElementField(FlowMacro::Vector::U0, "U0");
	field0Pointer.pressure = this->CreatScalarElementField(FlowMacro::Scalar::P0, "P0");
	for (int k = 0; k < turbulentStatus.nVariable; ++k)
	{
		const auto &marco = turbulentStatus.variableMacro0[k];
		const std::string &name = Configure::Flow::turbulenceVariableNameReverseMap.find(marco)->second;
		field0Pointer.turbulence.push_back(this->CreatScalarElementField(marco, name));
	}
    
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
	{
		const std::string &name = multiSpeciesStatus.speciesList[k];
		field0Pointer.massFraction.push_back(this->CreatScalarElementField(FlowMacro::Scalar::MASSFRACTION, name));
	}
#endif

    // 创建上一步物理场备份，用于多重网格延拓
    if(meshStruct.level > 0)
    {
	    field0PointerBackup.density =  new ElementField<Scalar>(meshStruct.mesh, Scalar0, "RHO0_BACKUP");
	    field0PointerBackup.velocity = new ElementField<Vector>(meshStruct.mesh, Vector0, "U0_BACKUP");
	    field0PointerBackup.pressure = new ElementField<Scalar>(meshStruct.mesh, Scalar0, "P0_BACKUP");
	    for (int k = 0; k < turbulentStatus.nVariable; ++k)
	    {
	    	const auto &marco = turbulentStatus.variableMacro0[k];
	    	const std::string name = Configure::Flow::turbulenceVariableNameReverseMap.find(marco)->second + "_BACKUP";
	    	field0PointerBackup.turbulence.push_back(new ElementField<Scalar>(meshStruct.mesh, Scalar0, name));
	    }
#if defined(_EnableMultiSpecies_)
		for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
		{
			const std::string &name = multiSpeciesStatus.speciesList[k];
			field0PointerBackup.massFraction.push_back(new ElementField<Scalar>(meshStruct.mesh, Scalar0, name));
		}
#endif
    }

	// 在新创建的物理场包中，创建残值场
	residualFieldPointer.residualMass     = this->CreatScalarElementField(FlowMacro::Scalar::RESIDUAL_MASS, "RESIDUAL_MASS");
	residualFieldPointer.residualMomentum = this->CreatVectorElementField(FlowMacro::Vector::RESIDUAL_MOMENTUM, "RESIDUAL_MOMENTUM");
	residualFieldPointer.residualEnergy   = this->CreatScalarElementField(FlowMacro::Scalar::RESIDUAL_ENERGY, "RESIDUAL_ENERGY");
    
	for (int k = 0; k < turbulentStatus.nVariable; ++k)
	{
		const auto &marco = turbulentStatus.residualMacro[k];
		const std::string &name = Configure::Flow::turbulenceResidualNameMap.find(marco)->second;
		residualFieldPointer.residualTurbulence.push_back(this->CreatScalarElementField(marco, name));
	}

#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
	{
		const std::string &name = multiSpeciesStatus.speciesList[k];
		residualFieldPointer.residualMassFraction.push_back(this->CreatScalarElementField(FlowMacro::Scalar::MASSFRACTION, name));
	}
#endif

    // 在新创建的物理场包中，创建梯度场，梯度场初始为空
	gradientFieldPointer.gradientRho = nullptr;
	gradientFieldPointer.gradientP = nullptr;
	gradientFieldPointer.gradientU = nullptr;
	gradientFieldPointer.gradientT = nullptr;
	for (int k = 0; k < turbulentStatus.nVariable; ++k) gradientFieldPointer.gradientTurbulence.push_back(nullptr);
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k) gradientFieldPointer.gradientMassFraction.push_back(nullptr);
#endif
	
	// 对流为二阶迎风格式，需要计算gradientRho、gradientU、gradientP
	const auto &inviscidOrder = flowConfigure.GetFluxScheme(meshStruct.level).reconstructOrder;
	const auto &inviscidScheme = flowConfigure.GetFluxScheme(meshStruct.level).inviscid;	
	if (inviscidOrder == Flux::ReconstructionOrder::SECOND && inviscidScheme != Flux::Flow::Inviscid::Scheme::CENTRAL)
	{
		gradientFieldPointer.gradientRho = this->CreatVectorElementField(FlowMacro::Vector::GRADIENT_RHO, "GRADIENT_RHO");
		gradientFieldPointer.gradientU = this->CreatTensorElementField(FlowMacro::Tensor::GRADIENT_U, "GRADIENT_U");
		gradientFieldPointer.gradientP = this->CreatVectorElementField(FlowMacro::Vector::GRADIENT_P, "GRADIENT_P");
	}

	// 粘性项不采用薄层假设，需要计算gradientU、gradientT
    const auto &viscousScheme = flowConfigure.GetFluxScheme(meshStruct.level).viscous;
	if (viscousScheme != Flux::Flow::Viscous::Scheme::CENTRAL_DISTANCE)
	{
		if (gradientFieldPointer.gradientU == nullptr)
		{
			gradientFieldPointer.gradientU = this->CreatTensorElementField(FlowMacro::Tensor::GRADIENT_U, "GRADIENT_U");
		}
		gradientFieldPointer.gradientT = this->CreatVectorElementField(FlowMacro::Vector::GRADIENT_T, "GRADIENT_T");
	}

	// 湍流源项计算需要速度梯度、湍流量梯度
	if (turbulentStatus.turbulenceFlag)
	{
		for (int k = 0; k < turbulentStatus.nVariable; ++k)
		{
			const auto &marco = turbulentStatus.gradientMacro[k];
			const std::string &name = Configure::Flow::turbulenceGradientNameMap.find(marco)->second;
			gradientFieldPointer.gradientTurbulence[k] = this->CreatVectorElementField(marco, name);
		}

		if (gradientFieldPointer.gradientU == nullptr)
		{
			gradientFieldPointer.gradientU = this->CreatTensorElementField(FlowMacro::Tensor::GRADIENT_U, "GRADIENT_U");
		}
	}

	// 隐式求解Jacobian矩阵
	implicitSolver.jacobian = nullptr;
	implicitSolver.jacobianTur = nullptr;
	implicitSolver.updateJacobian = false;
    if (flowConfigure.GetTimeScheme().innnerLoopType > Time::Scheme::ExactJacobian)
	{
		const int nVariable = meshStruct.dim2 ? 4 : 5;
		implicitSolver.updateJacobian = true;
		implicitSolver.implicitFlag = true;

		if (flowConfigure.GetTimeScheme().exactJacobian.solverType == Time::LinearSolverType::PRIVATE)
		{
			implicitSolver.jacobian = new BlockSparseMatrixSelf(meshStruct.mesh, nVariable);
			if (turbulentStatus.nVariable > 0) implicitSolver.jacobianTur = new BlockSparseMatrixSelf(meshStruct.mesh, turbulentStatus.nVariable);
		}
#if defined(_EnablePETSC_)
		else if (flowConfigure.GetTimeScheme().exactJacobian.solverType == Time::LinearSolverType::PETSC)
		{
			implicitSolver.jacobian = new BlockSparseMatrixPetsc(meshStruct.mesh, nVariable);
			if (turbulentStatus.nVariable > 0) implicitSolver.jacobianTur = new BlockSparseMatrixPetsc(meshStruct.mesh, turbulentStatus.nVariable);
		}
#endif
	}
	
	// 非定常相关物理场
    unsteadyFieldPointer.clear();
	if (unsteadyStatus.dualTime)
    {
        if(unsteadyStatus.unsteadyOrder == Time::UnsteadyOrder::FIRST)
            unsteadyFieldPointer.resize(2);
        else if(unsteadyStatus.unsteadyOrder == Time::UnsteadyOrder::SECOND)
            unsteadyFieldPointer.resize(3);
        else
            unsteadyFieldPointer.resize(1);
        
	    unsteadyFieldPointer[0].density = fieldPointer.density;
	    unsteadyFieldPointer[0].velocity = fieldPointer.velocity;
	    unsteadyFieldPointer[0].pressure = fieldPointer.pressure;
        unsteadyFieldPointer[0].turbulence = fieldPointer.turbulence;
#if defined(_EnableMultiSpecies_)
		unsteadyFieldPointer[0].massFraction = fieldPointer.massFraction;
#endif

        const int fieldSize = (int)unsteadyFieldPointer.size();
        for (int i = 1; i < fieldSize; ++i)
        {
	        unsteadyFieldPointer[i].density = new ElementField<Scalar>(meshStruct.mesh, Scalar0, fieldPointer.density->GetName() + "_t" + ToString(i));
	        unsteadyFieldPointer[i].velocity = new ElementField<Vector>(meshStruct.mesh, Vector0, fieldPointer.velocity->GetName() + "_t" + ToString(i));
	        unsteadyFieldPointer[i].pressure = new ElementField<Scalar>(meshStruct.mesh, Scalar0, fieldPointer.pressure->GetName() + "_t" + ToString(i));
            for (int k = 0; k < turbulentStatus.nVariable; ++k)
            {
                const auto &marco = turbulentStatus.variableMacro00[k];
                const std::string &name = Configure::Flow::turbulenceVariableNameReverseMap.find(marco)->second;
                unsteadyFieldPointer[i].turbulence.push_back(new ElementField<Scalar>(meshStruct.mesh, Scalar0, name + "_t" + ToString(i)));
            }
#if defined(_EnableMultiSpecies_)
			for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
			{
				const std::string &name = multiSpeciesStatus.speciesList[k];
				unsteadyFieldPointer[i].massFraction.push_back(new ElementField<Scalar>(meshStruct.mesh, Scalar0, name + "_t" + ToString(i)));
			}
#endif
        }
    }

	// 初始化创建重叠网格单元类型场
	fieldPointer.oversetElemType = nullptr;
	if (flowConfigure.JudgeEnableOversetMesh())
	{
		fieldPointer.oversetElemType = this->CreatIntElementField(FlowMacro::Int::OversetElemType, "OversetElemType");
	}

	// 初始化创建运动相关变量场
	fieldPointer.meshVelocity = NULL;
	if (flowConfigure.JudgeEnableMotion())
	{
		fieldPointer.meshVelocity = this->CreatVectorElementField(FlowMacro::Vector::meshVelocity, "meshVelocity");
	}

#if defined(_EnableFlutter_)
	// 初始化摄动场
	fieldPointer.perturbationFields.clear();
	fieldPointer.wallPerturbationFields.clear();
#endif
}

void FlowPackage::GetConservedValue(const int &elementID, Scalar &rho, Vector &rhoU, Scalar &rhoE)
{
	const Vector &UTemp = fieldPointer.velocity->GetValue(elementID);
	const Scalar &pTemp = fieldPointer.pressure->GetValue(elementID);
	rho = fieldPointer.density->GetValue(elementID);
	rhoU = rho * UTemp;
	rhoE = pTemp / materialNumber.gamma1 + 0.5 * rho * (UTemp & UTemp);
}

void FlowPackage::GetConservedValue0(const int &elementID, Scalar &rho, Vector &rhoU, Scalar &rhoE)
{
	const Vector &UTemp = field0Pointer.velocity->GetValue(elementID);
	const Scalar &pTemp = field0Pointer.pressure->GetValue(elementID);
	rho = field0Pointer.density->GetValue(elementID);
	rhoU = rho * UTemp;
	rhoE = pTemp / materialNumber.gamma1 + 0.5 * rho * (UTemp & UTemp);
}

ElementField<Scalar>* FlowPackage::GetTurbulencePointer(const FlowMacro::Scalar &macro)
{
	for (int k = 0; k < turbulentStatus.nVariable; ++k)
	{
		if (turbulentStatus.variableMacro[k] == macro)
			return fieldPointer.turbulence[k];
	}

	return nullptr;
}

void FlowPackage::SetPrimitiveValue(const int &elementID, const Scalar &rho, const Vector &rhoU, const Scalar &rhoE)
{
    const Scalar rhoInv = 1.0 / rho;
    const Scalar pTemp = (rhoE - 0.5 * rhoInv * (rhoU & rhoU)) * materialNumber.gamma1;
    fieldPointer.density->SetValue(elementID, rho);
    fieldPointer.velocity->SetValue(elementID, rhoU * rhoInv);
    fieldPointer.pressure->SetValue(elementID, pTemp);
}

void FlowPackage::CalcuateConservedValue(const Scalar &rho, const Vector &U, const Scalar &p, Vector &rhoU, Scalar &rhoE)
{
	rhoU = rho * U;
	rhoE = p / materialNumber.gamma1 + 0.5 * rho * (U & U);
}

Scalar FlowPackage::CalculateMu(const int &elementID)
{	
	Scalar mu = fieldPointer.muLaminar->GetValue(elementID);
	if (fieldPointer.muTurbulent != nullptr) mu += fieldPointer.muTurbulent->GetValue(elementID);
	return mu;
}

Scalar FlowPackage::CalculateKappa(const int &elementID)
{
	Scalar kappa;
#if defined(_EnableMultiSpecies_)
	if (flowConfigure.GetMaterial().density != Material::Flow::DensityType::IDEAL_GAS)
	{
		const int &specisSize = material.GetSpeciesSize();
		std::vector<Scalar> massFractionTemp(specisSize);
		for (int k = 0; k < specisSize; k++) massFractionTemp[k] = fieldPointer.massFraction[k]->GetValue(elementID);
		kappa = material.Kappa(fieldPointer.temperature->GetValue(elementID), massFractionTemp);
	}
	else
#endif
	{
		kappa = materialNumber.Cp * fieldPointer.muLaminar->GetValue(elementID) / materialNumber.PrandtlLaminar;
	}

	if (fieldPointer.muTurbulent != nullptr)
        kappa += materialNumber.Cp * fieldPointer.muTurbulent->GetValue(elementID) / materialNumber.PrandtlTurbulent;
    
	return kappa;
}

Tensor FlowPackage::CalculateTau(const Scalar &mu, const Tensor &gradientU)
{
    Tensor symmetricTensor = gradientU.Symm(false);
    // symmetricTensor.Trace()中已包含两倍，此处乘0.5
    Scalar diagValue = 0.5 * materialNumber.stokes * symmetricTensor.Trace();
    symmetricTensor.AddDiag(diagValue);
	return mu * symmetricTensor;
}

void FlowPackage::UpdateExtrasField()
{
	Mesh *mesh = meshStruct.mesh;
	const int elementNumber = mesh->GetElementNumberInDomain();
#if defined(_EnableMultiSpecies_)
	if (flowConfigure.GetMaterial().density != Material::Flow::DensityType::IDEAL_GAS)
	{
		const int &specisSize = material.GetSpeciesSize();
		std::vector<Scalar> massFractionTemp(specisSize);
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &pTemp = fieldPointer.pressure->GetValue(elementID);
			const Scalar &rhoTemp = fieldPointer.density->GetValue(elementID);
			for (int k = 0; k < specisSize; k++)
			{
				massFractionTemp[k] = fieldPointer.massFraction[k]->GetValue(elementID);
			}
			const Scalar TTemp = material.GetTemperature(pTemp, rhoTemp, massFractionTemp);
			fieldPointer.temperature->SetValue(elementID, TTemp);
			fieldPointer.soundSpeed->SetValue(elementID, material.GetSoundSpeed(TTemp, massFractionTemp));
			fieldPointer.enthalpy->SetValue(elementID, material.Enthalpy(TTemp, massFractionTemp));
			if (fieldPointer.muLaminar != nullptr)
				fieldPointer.muLaminar->SetValue(elementID, material.Mu(TTemp, massFractionTemp));
		}
	}
	else
#endif
	{
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &pTemp = fieldPointer.pressure->GetValue(elementID);
			const Scalar &rhoTemp = fieldPointer.density->GetValue(elementID);
			const Scalar TTemp = material.GetTemperature(pTemp, rhoTemp);
			fieldPointer.temperature->SetValue(elementID, TTemp);
			fieldPointer.soundSpeed->SetValue(elementID, material.GetSoundSpeed(TTemp));
			fieldPointer.enthalpy->SetValue(elementID, material.Enthalpy(TTemp));
			if (fieldPointer.muLaminar != nullptr)
				fieldPointer.muLaminar->SetValue(elementID, material.Mu(TTemp));
		}
	}
}

void FlowPackage::CalculatePressureSwitch()
{
	ElementField<Scalar> *pressure = fieldPointer.pressure;
	ElementField<Scalar> *pressureSwitch = fieldPointer.pressureSwitch;
	ElementField<Scalar> *pressureSum = &this->GetTempElementField("pressureSum", Scalar0);
	ElementField<Scalar> *pressureDelta = &this->GetTempElementField("pressureDelta", Scalar0);
	pressureSwitch->Initialize();
	
	Mesh *mesh = meshStruct.mesh;
	const bool &nodeCenter = flowConfigure.GetPreprocess().dualMeshFlag;

	if (nodeCenter)
	{
		// 内部面循环
    	const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    	for (int index = 0; index < innerFaceNumber; ++index)
    	{
			// 得到面相关信息
    	    const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();

			// 边界边对应的面单独处理
            if (mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID)) continue;

			//累计压力探测器值
    	    const Scalar pSum = pressure->GetValue(neighID) + pressure->GetValue(ownerID);
    	    const Scalar pDelta = pressure->GetValue(neighID) - pressure->GetValue(ownerID);
			pressureDelta->AddValue(ownerID,  pDelta);
			pressureDelta->AddValue(neighID, -pDelta);
			pressureSum->AddValue(ownerID, pSum);
			pressureSum->AddValue(neighID, pSum);
		}
		
        // 对称面
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
            const Boundary::Type &typeString = this->GetLocalBoundaryType(patchID);
			if (typeString == Boundary::Type::SYMMETRY)
			{
				const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
				for (int index = 0; index < faceSize; ++index)
				{
					// 得到面相关信息
					const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
					const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
					pressureDelta->MultiplyValue(ownerID, 2.0);
					pressureSum->MultiplyValue(ownerID, 2.0);
				}
			}
		}

		// 内部面循环
    	for (int index = 0; index < innerFaceNumber; ++index)
    	{
			// 得到面相关信息
    	    const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            // 边界边对应的面单独处理
            if (!mesh->JudgeHalfFaceCrossSymmetryBoundary(faceID)) continue;

			//累计压力探测器值
			const Scalar pSum = pressure->GetValue(neighID) + pressure->GetValue(ownerID);
			const Scalar pDelta = pressure->GetValue(neighID) - pressure->GetValue(ownerID);
			pressureDelta->AddValue(ownerID, pDelta);
			pressureDelta->AddValue(neighID, -pDelta);
			pressureSum->AddValue(ownerID, pSum);
			pressureSum->AddValue(neighID, pSum);
		}
	}
	else
	{
		// 内部面循环
    	const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    	for (int index = 0; index < innerFaceNumber; ++index)
    	{
			// 得到面相关信息
    	    const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const Face &face = mesh->GetFace(faceID);
			const int &ownerID = face.GetOwnerID();
			const int &neighID = face.GetNeighborID();

			//累计压力探测器值
    	    const Scalar pSum = pressure->GetValue(neighID) + pressure->GetValue(ownerID);
    	    const Scalar pDelta = pressure->GetValue(neighID) - pressure->GetValue(ownerID);

			pressureDelta->AddValue(ownerID,  pDelta);
			pressureDelta->AddValue(neighID, -pDelta);
			pressureSum->AddValue(ownerID, pSum);
			pressureSum->AddValue(neighID, pSum);
		}
		
    	// 边界面循环
		const int &boundarySize = mesh->GetBoundarySize();
		for (int patchID = 0; patchID < boundarySize; ++patchID)
		{
			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				// 得到面相关信息
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const Face &face = mesh->GetFace(faceID);
				const int &ownerID = face.GetOwnerID();
				const int &neighID = face.GetNeighborID();
	
				//累计压力探测器值
    	    	const Scalar pSum = pressure->GetValue(neighID) + pressure->GetValue(ownerID);
    	    	const Scalar pDelta = pressure->GetValue(neighID) - pressure->GetValue(ownerID);
	
				pressureDelta->AddValue(ownerID,  pDelta);
				pressureSum->AddValue(ownerID, pSum);
			}
		}
	}
	
	//计算压力探测器
	pressureSwitch->Initialize();
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar temp = fabs(pressureDelta->GetValue(elementID)) / (pressureSum->GetValue(elementID) + SMALL);
		pressureSwitch->SetValue(elementID, temp);
	}
    pressureSwitch->SetGhostlValueParallel();
	pressureSwitch->SetGhostlValueOverset();
    
    this->FreeTempField(*pressureSum);
    this->FreeTempField(*pressureDelta);

	return;
}

void FlowPackage::UpdateCurrentTime(const Scalar &timeStep)
{
    const Scalar timeStepTemp = Min(timeStep, unsteadyStatus.totalTime - unsteadyStatus.currentTime);
    this->unsteadyStatus.timeStep = timeStepTemp;
    this->unsteadyStatus.currentTime += timeStepTemp;
}

void FlowPackage::PreCalculate()
{
	Mesh *mesh = meshStruct.mesh;
	const int &faceNumber = mesh->GetFaceNumber();
	extraInfo.distance.resize(faceNumber);
	extraInfo.distanceNormal.resize(faceNumber, Vector0);

    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		const Face &face = mesh->GetFace(faceID);
		const int &ownerID = face.GetOwnerID();
		const int &neighID = face.GetNeighborID();

		const Vector d = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
		extraInfo.distance[faceID] = d.Mag();
		if (!extraInfo.distanceNormal.empty()) extraInfo.distanceNormal[faceID] = d.GetNormal();
	}

	const bool &dualFlag = flowConfigure.GetPreprocess().dualMeshFlag;
	for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
	{
		const int faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
			const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();

            Vector d;
            if (dualFlag)
            {
                const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);
                d = mesh->GetElement(ownerID).GetCenter() - mesh->GetElement(innerID).GetCenter();
            }
            else
            {
                const int &neighID = face.GetNeighborID();
                d = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            }

			extraInfo.distance[faceID] = d.Mag();
            if (!extraInfo.distanceNormal.empty()) extraInfo.distanceNormal[faceID] = d.GetNormal();
		}
	}
}

void FlowPackage::InitializeField(const Initialization::Type &initialType)
{
	auto &rho = *(fieldPointer.density);
	auto &U = *(fieldPointer.velocity);
	auto &p = *(fieldPointer.pressure);
	auto &turbulence = fieldPointer.turbulence;

	const bool &resetAverage = flowConfigure.GetControl().outerLoop.resetAverage;

	// 原始量初始化
	if (initialType == Initialization::Type::REFERENCE)
	{
		rho.Initialize(flowConfigure.GetFlowReference().density);
		U.Initialize(flowConfigure.GetFlowReference().velocity);
		p.Initialize(flowConfigure.GetFlowReference().staticPressure);
		for (int m = 0; m < turbulence.size(); ++m)
			turbulence[m]->Initialize((*(turbulentStatus.freeStreamValue))[m]);

#if defined(_EnableMultiSpecies_)
		auto &massFraction = fieldPointer.massFraction;
		for (int k = 0; k < massFraction.size(); ++k)
			massFraction[k]->Initialize(multiSpeciesStatus.massFractionR[k]);
#endif
	}
	else if (initialType == Initialization::Type::STATIC_FLOW)
	{
		rho.Initialize(flowConfigure.GetFlowReference().density);
		U.Initialize(Vector0);
		p.Initialize(flowConfigure.GetFlowReference().staticPressure);
		for (int m = 0; m < turbulence.size(); ++m)
			turbulence[m]->Initialize(Scalar0);
	}
	else if (initialType == Initialization::Type::RESTART)
	{
		if (this->meshStruct.level > 0)
		{
			FatalError("FlowPackage::InitializeField: RESTART initialization must be level 0!");
			return;
		}
		
		const std::string &resultPath = flowConfigure.GetControl().resultSavePath;
		const std::string restartStep = ToString(flowConfigure.GetControl().initialization.restartStep);
		const std::string folderPath = resultPath + restartStep + "/";
		const int zoneID = this->GetMeshStruct().mesh->GetMeshZoneID();  //增加对多域的支持
	
		this->InitializeFromGlobal(rho, folderPath + rho.GetName() + "_zone" + ToString(zoneID) + ".Field");
		this->InitializeFromGlobal(U, folderPath + U.GetName() + "_zone" + ToString(zoneID) + ".Field");
		this->InitializeFromGlobal(p, folderPath + p.GetName() + "_zone" + ToString(zoneID) + ".Field");

		bool turbulenceFlag = true;
		for (int m = 0; m < turbulence.size(); ++m)
		{
			std::string stringTemp = folderPath + turbulence[m]->GetName() + "_zone" + ToString(zoneID) + ".Field";
			std::ifstream file(stringTemp.c_str());
			if (!file.good()) turbulenceFlag = false;
		}
		if (turbulenceFlag)
		{
		for (int m = 0; m < turbulence.size(); ++m)
			this->InitializeFromGlobal(*turbulence[m], folderPath + turbulence[m]->GetName() + "_zone" + ToString(zoneID) + ".Field");
		}
		else
		{
			for (int m = 0; m < turbulence.size(); ++m)
				turbulence[m]->Initialize((*(turbulentStatus.freeStreamValue))[m]);
		}

#if defined(_EnableMultiSpecies_)
		bool massFractionFlag = true;
		auto &massFraction = fieldPointer.massFraction;
		for (int m = 0; m < massFraction.size(); ++m)
		{
			std::string stringTemp = folderPath + massFraction[m]->GetName() + "_zone" + ToString(zoneID) + ".Field";
			std::ifstream file(stringTemp.c_str());
			if (!file.good()) massFractionFlag = false;
		}
		if (massFractionFlag)
		{
			for (int m = 0; m < massFraction.size(); ++m)
				this->InitializeFromGlobal(*massFraction[m], folderPath + massFraction[m]->GetName() + "_zone" + ToString(zoneID) + ".Field");
		}
		else
		{
			FatalError("FlowPackage::InitializeField: massFraction field is missing!");
		}
#endif
		
		if (unsteadyStatus.dualTime && !resetAverage)
		{
			auto &meanMu = *(fieldPointer.meanMu);
			auto &meanMuTurbulent = *(fieldPointer.meanMuTurbulent);
			auto &meanPressure = *(fieldPointer.meanPressure);
			auto &meanVelocity = *(fieldPointer.meanVelocity);

			this->InitializeFromGlobal(meanMu, folderPath + meanMu.GetName() + "_zone" + ToString(zoneID) + ".Field");
			this->InitializeFromGlobal(meanMuTurbulent, folderPath + meanMuTurbulent.GetName() + "_zone" + ToString(zoneID) + ".Field");
			this->InitializeFromGlobal(meanPressure, folderPath + meanPressure.GetName() + "_zone" + ToString(zoneID) + ".Field");
			this->InitializeFromGlobal(meanVelocity, folderPath + meanVelocity.GetName() + "_zone" + ToString(zoneID) + ".Field");

			meanMu.SetGhostlValueBoundary();
			meanMuTurbulent.SetGhostlValueBoundary();
			meanPressure.SetGhostlValueBoundary();
			meanVelocity.SetGhostlValueBoundary();

		}
	}
	else if (initialType == Initialization::Type::FILE)
	{
		if (this->meshStruct.level > 0)
		{
			FatalError("FlowPackage::InitializeField: FILE initialization must be level 0!");
			return;
		}
		
		const std::string folderPath = flowConfigure.GetControl().initialization.initialFilePath;
		const int zoneID = this->GetMeshStruct().mesh->GetMeshZoneID();  //增加对多域的支持

		this->InitializeFromGlobal(rho, folderPath + rho.GetName() + "_zone" + ToString(zoneID) + ".Field");
		this->InitializeFromGlobal(U, folderPath + U.GetName() + "_zone" + ToString(zoneID) + ".Field");
		this->InitializeFromGlobal(p, folderPath + p.GetName() + "_zone" + ToString(zoneID) + ".Field");

		bool existFlag = true;
		for (int m = 0; m < turbulence.size(); ++m)
		{
			std::string stringTemp = folderPath + turbulence[m]->GetName() + "_zone" + ToString(zoneID) + ".Field";
			std::ifstream file(stringTemp.c_str());
			if (!file.good()) existFlag = false;
		}

		if (existFlag)
		{
		for (int m = 0; m < turbulence.size(); ++m)
			this->InitializeFromGlobal(*turbulence[m], folderPath + turbulence[m]->GetName() + "_zone" + ToString(zoneID) + ".Field");
		}
		else
		{
			for (int m = 0; m < turbulence.size(); ++m)
				turbulence[m]->Initialize((*(turbulentStatus.freeStreamValue))[m]);
		}
		
#if defined(_EnableMultiSpecies_)
		bool massFractionFlag = true;
		auto &massFraction = fieldPointer.massFraction;
		for (int m = 0; m < massFraction.size(); ++m)
		{
			std::string stringTemp = folderPath + massFraction[m]->GetName() + "_zone" + ToString(zoneID) + ".Field";
			std::ifstream file(stringTemp.c_str());
			if (!file.good()) massFractionFlag = false;
		}
		if (massFractionFlag)
		{
			for (int m = 0; m < massFraction.size(); ++m)
				this->InitializeFromGlobal(*massFraction[m], folderPath + massFraction[m]->GetName() + "_zone" + ToString(zoneID) + ".Field");
		}
		else
		{
			FatalError("FlowPackage::InitializeField: massFraction field is missing!");
		}
#endif
	}
	else if (initialType == Initialization::Type::NONE_INITIAL)
	{
	}
	else
	{
		FatalError("FlowPackage::Initialize: InitialType is error!");
		return;
	}

	// 初始化其他相关物理场
	this->UpdateExtrasField();
	
	// 初始化非定常计算物理场
	this->InitializeUnsteadyField();
}

template void FlowPackage::InitializeFromGlobal(ElementField<Scalar> &phiLocal, const std::string &fieldName);
template void FlowPackage::InitializeFromGlobal(ElementField<Vector> &phiLocal, const std::string &fieldName);
template<class Type>
void FlowPackage::InitializeFromGlobal(ElementField<Type> &phiLocal, const std::string &fieldName)
{
	if (this->meshStruct.level > 0)
	{
		FatalError("FlowPackage::InitializeFromGlobal: InitializeFromGlobal must be level 0!");
		return;
	}
	
	std::vector<Type> phiGlobal;
	std::fstream file;
	file.open(fieldName, std::ios::in | std::ios::binary);
	if (!file.is_open()) return;
	IO::Read(file, phiGlobal, true);
	file.close();
	SubMesh *subMesh = (SubMesh *) this->meshStruct.mesh;
	const int elementNumber = subMesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &localID = subMesh->GetElementIDInDomain(index);
		const int &globalID = subMesh->GetElementGlobalID(localID);
		phiLocal.SetValue(localID, phiGlobal[globalID]);
	}

	int elementSizeGlobal = subMesh->GetElementNumberReal();
	SumAllProcessor(elementSizeGlobal, 0);
	MPIBroadcast(elementSizeGlobal, 0);

	const int boundarySize = subMesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = subMesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = subMesh->GetBoundaryFaceIDInDomain(patchID, index);
			const int &globalID = subMesh->GetFaceGlobalID(faceID);
			const int &ownerID = subMesh->GetFace(faceID).GetOwnerID();
			const int &neighID = subMesh->GetFace(faceID).GetNeighborID();
			phiLocal.SetValue(neighID, 2.0 * phiGlobal[elementSizeGlobal + globalID] - phiLocal.GetValue(ownerID));
		}
	}
	std::vector<Type>().swap(phiGlobal);

	return;
}

void FlowPackage::InitializeUnsteadyField()
{
	const int unsteadyFieldSize = (int)unsteadyFieldPointer.size();
	for (int i = 1; i < unsteadyFieldSize; i++)
	{
		*unsteadyFieldPointer[i].density  = *unsteadyFieldPointer[0].density;
		*unsteadyFieldPointer[i].velocity = *unsteadyFieldPointer[0].velocity;
		*unsteadyFieldPointer[i].pressure = *unsteadyFieldPointer[0].pressure;
		for (int k = 0; k < this->GetTurbulentStatus().nVariable; k++)
			*unsteadyFieldPointer[i].turbulence[k] = *unsteadyFieldPointer[0].turbulence[k];
			
#if defined(_EnableMultiSpecies_)
		for (int k = 0; k < multiSpeciesStatus.speciesSize; ++k)
			*unsteadyFieldPointer[i].massFraction[k] = *unsteadyFieldPointer[0].massFraction[k];
#endif
	}
}

#if defined(_EnableFlutter_)
void FlowPackage::InitializePerturbationFields(const std::vector<std::vector<Vector>>& perturbationData)
{
    if (GetMPIRank() == 0) {
        Print("初始化摄动场数据，模态数量: " + ToString(perturbationData.size()));
    }

    // 清理现有的摄动场
    for (auto* field : fieldPointer.perturbationFields) {
        if (field != nullptr) {
            delete field;
        }
    }
    fieldPointer.perturbationFields.clear();
    fieldPointer.wallPerturbationFields.clear();

    // 创建全场摄动场
    int numModes = perturbationData.size();
    fieldPointer.perturbationFields.resize(numModes);

    for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
        std::string fieldName = "PerturbationField_Mode" + ToString(modeIdx + 1);
        fieldPointer.perturbationFields[modeIdx] = new ElementField<Vector>(meshStruct.mesh, Vector0, fieldName);

        // 将摄动场数据复制到场中
        const auto& modeData = perturbationData[modeIdx];
        int nodeNumber = modeData.size();

        if (nodeNumber != meshStruct.mesh->GetNodeNumberReal()) {
            FatalError("摄动场节点数量与网格节点数量不匹配: " + ToString(nodeNumber) + " vs " + ToString(meshStruct.mesh->GetNodeNumberReal()));
        }

        // 将节点数据复制到场中
        for (int nodeIdx = 0; nodeIdx < nodeNumber; nodeIdx++) {
            fieldPointer.perturbationFields[modeIdx]->SetValue(nodeIdx, modeData[nodeIdx]);
        }
    }

    // 从全场摄动场中提取物面摄动场
    ExtractWallPerturbationFields();

    if (GetMPIRank() == 0) {
        Print("摄动场数据初始化完成，物面节点数量: " + ToString(fieldPointer.wallPerturbationFields.empty() ? 0 : fieldPointer.wallPerturbationFields[0].size()));
    }
}

void FlowPackage::ExtractWallPerturbationFields()
{
    if (GetMPIRank() == 0) {
        Print("从全场摄动场中提取物面摄动场...");
    }

    Mesh* mesh = meshStruct.mesh;
    int numModes = fieldPointer.perturbationFields.size();

    // 收集所有物面节点
    std::vector<int> wallNodeIDs;
    std::set<int> wallNodeSet; // 用于去重

    // 遍历所有壁面边界
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID) {
        const auto& boundaryType = this->GetLocalBoundaryType(patchID);
        if (boundaryType == Boundary::Type::WALL || boundaryType == Boundary::Type::VISCOUS_WALL) {
            int faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int faceIdx = 0; faceIdx < faceSize; ++faceIdx) {
                int faceID = mesh->GetBoundaryFaceIDInDomain(patchID, faceIdx);
                const Face& face = mesh->GetFace(faceID);

                // 获取面的所有节点
                const auto& faceNodes = face.GetNodes();
                for (const auto& node : faceNodes) {
                    int nodeID = node.GetID();
                    if (wallNodeSet.find(nodeID) == wallNodeSet.end()) {
                        wallNodeSet.insert(nodeID);
                        wallNodeIDs.push_back(nodeID);
                    }
                }
            }
        }
    }

    int numWallNodes = wallNodeIDs.size();
    if (GetMPIRank() == 0) {
        Print("找到物面节点数量: " + ToString(numWallNodes));
    }

    // 初始化物面摄动场
    fieldPointer.wallPerturbationFields.resize(numModes);
    for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
        fieldPointer.wallPerturbationFields[modeIdx].resize(numWallNodes);

        // 从全场摄动场中提取物面节点的值
        for (int wallNodeIdx = 0; wallNodeIdx < numWallNodes; wallNodeIdx++) {
            int nodeID = wallNodeIDs[wallNodeIdx];
            fieldPointer.wallPerturbationFields[modeIdx][wallNodeIdx] =
                fieldPointer.perturbationFields[modeIdx]->GetValue(nodeID);
        }
    }

    // 存储物面节点ID映射，供后续使用
    this->wallNodeMapping = wallNodeIDs;

    if (GetMPIRank() == 0) {
        Print("物面摄动场提取完成");
    }
}
#endif

}//namespace Package

# 颤振计算框架修改说明

## 概述
本次修改将颤振计算框架从物理位移/力系统改为广义位移/力系统，基于模态坐标进行结构动力学计算。

## 主要修改内容

### 1. 新增文件
- `sourceFlow/flowSolver/StructuralDynamics.h` - 结构动力学类头文件
- `sourceFlow/flowSolver/StructuralDynamics.cpp` - 结构动力学类实现
- `modal_data_example.dat` - 模态数据文件示例

### 2. 修改的文件

#### sourceFlow/flowSolver/OuterLoop.h
- 添加了 `#include "sourceFlow/flowSolver/StructuralDynamics.h"`
- 修改了 `ComputeAerodynamicForces()` 为 `ComputeGeneralizedAerodynamicForces()`
- 添加了 `ConvertGeneralizedToPhysicalDisplacement()` 方法

#### sourceFlow/flowSolver/OuterLoop.cpp
- 修改了颤振计算流程，使用广义坐标系统
- 实现了 `ComputeGeneralizedAerodynamicForces()` 方法
- 实现了 `ConvertGeneralizedToPhysicalDisplacement()` 方法
- 添加了 `InitializeFlutterCalculation()` 方法
- 修改了强耦合和弱耦合的计算逻辑

#### sourceFlow/configure/FlowConfigure.h
- 添加了 `FlutterStruct` 结构体
- 添加了颤振参数的访问方法 `GetFlutter()` 和 `SetFlutter()`

#### meshProcess/motion/MotionManager.h
- 添加了 `UpdateFlutterMesh()` 方法（兼容性）
- 添加了 `UpdateFlutterMeshWithDisplacements()` 方法

#### meshProcess/motion/MotionManager.cpp
- 实现了颤振网格更新方法

## 技术特点

### 1. 广义坐标系统
- 使用模态坐标 {q} 代替物理位移
- 求解广义运动方程：[M]{q̈} + [C]{q̇} + [K]{q} = {Q}
- 广义质量、刚度、阻尼矩阵为对角矩阵（正交模态）

### 2. 气动力计算
- 计算物理气动力后投影到模态上得到广义气动力
- 公式：Q_i = ∫ F_aero · φ_i dS
- 其中 φ_i 是第i阶模态振型

### 3. 位移转换
- 通过模态叠加将广义位移转换为物理位移
- 公式：u = Σ(q_i × φ_i)

### 4. 时间积分
- 使用Newmark-β方法求解广义运动方程
- 支持自定义β和γ参数
- 默认值：β=0.25, γ=0.5（平均加速度法）

## 配置参数

### FlutterStruct 参数说明
```cpp
struct FlutterStruct
{
    bool enableFlutter;           // 是否启用颤振计算
    bool strongCoupling;          // 是否使用强耦合
    int maxFSIIterations;         // 最大FSI迭代次数
    double fsiTolerance;          // FSI收敛容差
    std::string modalDataFile;    // 模态数据文件路径
    std::vector<double> dampingRatios; // 各模态阻尼比
    double newmarkBeta;           // Newmark-β参数
    double newmarkGamma;          // Newmark-γ参数
};
```

## 模态数据文件格式

文件格式：
```
模态数量
固有频率1 (rad/s)
节点数量1
x1 y1 z1 dx1 dy1 dz1
x2 y2 z2 dx2 dy2 dz2
...
固有频率2 (rad/s)
节点数量2
...
```

## 使用方法

1. 准备模态数据文件（参考 `modal_data_example.dat`）
2. 在配置文件中设置颤振参数
3. 编译时启用 `_EnableFlutter_` 宏定义
4. 运行计算

## 优势

1. **计算效率高**：只需求解少数几个模态的运动方程
2. **数值稳定性好**：避免了大规模矩阵求解
3. **物理意义清晰**：直接基于结构模态特性
4. **易于扩展**：可方便添加更多模态或修改阻尼模型

## 注意事项

1. 模态数据需要从结构分析软件（如Nastran）中提取
2. 模态振型需要在流体网格节点上插值
3. 当前使用最近邻插值，实际应用中可考虑更高精度的插值方法
4. 需要确保模态的正交性和归一化

## 后续开发建议

1. 实现更精确的模态振型插值方法（如RBF插值）
2. 添加模态数据的自动读取和验证功能
3. 支持更多的结构阻尼模型
4. 添加颤振稳定性分析功能
5. 优化FSI耦合算法的收敛性

#include "sourceFlow/flowSolver/OuterLoop.h"

OuterLoop::OuterLoop(SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfigure_)
	: subMesh(subMesh_), flowConfigure(flowConfigure_)
{
	// 创建物理场包容器
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; level++)
	{
		// 依据网格层级编号level，获取相应层级网格，并以此网格定义物理场包，同时将物理场包置于物理场包容器中
		flowPackageVector.push_back(new Package::FlowPackage(level, subMesh, flowConfigure));
	}

	// 获取非定常标识
	unsteady = flowPackageVector[0]->GetUnsteadyStatus().unsteadyFlag;
	dualTime = flowPackageVector[0]->GetUnsteadyStatus().dualTime;

	fullMultigirdFlag = true;
	if (unsteady
		|| flowConfigure.GetControl().initialization.type == Initialization::Type::RESTART
		|| flowConfigure.GetControl().initialization.fullMultigridSteps <= 0)
	{
		fullMultigirdFlag = false;
	}

	// 获取初始化类型
	initialType = flowConfigure.GetControl().initialization.type;

	// 根据输入参数，确定多重网格总层数
	multigridLevel = flowConfigure.GetAcceleration().multigridSolver.level;

	// 依据输入参数中的时间格式，创建当前网格层级上物理场包更新的时间推进对象，并将其置于时间推进对象容器中        
	for (int level = 0; level < multigridLevel; level++)
		timeSchemeVector.push_back(new Time::Flow::FlowTimeManager(*flowPackageVector[level]));

	// 创建残值容器
	resultProcess = new FlowResultsProcess(subMesh, flowPackageVector);

	// 创建全多重求解器
	fullMultigird = nullptr;
	if (fullMultigirdFlag) fullMultigird = new FullMultigird(subMesh, flowPackageVector, timeSchemeVector, resultProcess);

	// 创建内循环求解器
	innerLoop = new InnerLoop(subMesh, flowPackageVector, timeSchemeVector, resultProcess);

#if defined(_EnableFlutter_)
	// 初始化颤振计算
	InitializeFlutterCalculation();
#endif
}

OuterLoop::~OuterLoop()
{
	if (resultProcess != nullptr) { delete resultProcess; resultProcess = nullptr; }

	for (int i = 0; i < timeSchemeVector.size(); i++)
	{
		if (timeSchemeVector[i] != nullptr)
		{
			delete timeSchemeVector[i];
			timeSchemeVector[i] = nullptr;
		}
	}

	if (fullMultigird != nullptr) { delete fullMultigird; fullMultigird = nullptr; }
	if (innerLoop != nullptr) { delete innerLoop; innerLoop = nullptr; }

	for (int i = 0; i < flowPackageVector.size(); i++)
	{
		if (flowPackageVector[i] != nullptr)
		{
			delete flowPackageVector[i];
			flowPackageVector[i] = nullptr;
		}
	}
}

void OuterLoop::Initialize()
{
	MPIBarrier();

	if (GetMPIRank() == 0) Print("\nInitialize flow fields ...");

	// 物理场初始化
	flowPackageVector[0]->InitializeField(initialType);

	// 时间格式初始化
	timeSchemeVector[0]->Initialize(initialType);

	// 残差监控初始化
	resultProcess->Initialize(0);

	// 保存初始物理场
	const int innerInterval = flowConfigure.GetControl().innerLoop.interval;
	if (innerInterval > 0) resultProcess->SaveIntervalResults();
}

void OuterLoop::CloseMultigrid()
{
    innerLoop->CloseMultigrid();
}

void OuterLoop::Solve()
{
	MPIBarrier();

	// 全多重初始化流场
	if (fullMultigirdFlag)
	{
		if (GetMPIRank() == 0) Print("\n  Initialize with full multigird ...");
		fullMultigird->Solve();
	}

	// 获取外循环流场输出间隔及总迭代步数
	const int &outerInterval = flowConfigure.GetControl().outerLoop.interval;
	const int &outerLoopSteps = flowConfigure.GetControl().outerLoop.steps;
	const int &averagedStep = flowConfigure.GetControl().outerLoop.averagedStep;
	int computeAveragedStep = 0;

	// 非定常计算总物理时间及当前物理时间
	const Scalar &totalTime = flowPackageVector[0]->GetUnsteadyStatus().totalTime;
	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;

	if (GetMPIRank() == 0) Print("\nBegin to solve ...");

	// 非定常计算,流场迭代求解外循环（物理时间迭代）
	for (int outerStep = 0; outerStep < outerLoopSteps; outerStep++)
	{
		// 非定常计算时保存上一时间步物理场
		if(dualTime)
		{
			resultProcess->Initialize(0);
			this->SetTimeStep();
			this->UpdateOldField(outerStep);
		}

#if defined(_EnableFlutter_)
		// 强耦合颤振计算
		if (enableFlutter && flowConfigure.GetFlutter().strongCoupling) {

			bool fsiConverged = false;
			int fsiIter = 0;
			const int maxFSIIter = flowConfigure.GetFlutter().maxFSIIterations;
			const double fsiTolerance = flowConfigure.GetFlutter().fsiTolerance;

			std::vector<double> dispOld = structuralDynamics->GetGeneralizedDisplacement();
			
			while (!fsiConverged && fsiIter < maxFSIIter) {
				
				// 1. 内迭代求解流场
				innerLoop->Solve();
				
				// 2. 计算广义气动力
				std::vector<double> generalizedAeroForces = this->ComputeGeneralizedAerodynamicForces();

				// 3. 求解广义结构运动方程
				if (outerStep > 0) {
					structuralDynamics->SolveGeneralizedEquation(generalizedAeroForces, physicalTimeStep);
				}

				// 4. 检查FSI收敛性（基于广义位移）
				const auto& generalizedDispNew = structuralDynamics->GetGeneralizedDisplacement();
				double maxDispChange = 0.0;
				for (int i = 0; i < generalizedDispNew.size(); i++) {
					maxDispChange = Max(maxDispChange, abs(generalizedDispNew[i] - dispOld[i]));
				}

				if (maxDispChange < fsiTolerance) {
					fsiConverged = true;
				} else {
					// 5. 将广义位移转换为物理位移并更新网格
					this->ConvertGeneralizedToPhysicalDisplacement(generalizedDispNew);
					this->UpdateMeshMetrics();
					dispOld = generalizedDispNew;
				}
				
				fsiIter++;
			}
			
			if (!fsiConverged && GetMPIRank() == 0) {
				Print("Warning: FSI not converged at time step " + ToString(outerStep));
			}
		}
		else if (enableFlutter) {
			// 弱耦合颤振计算（广义坐标方案）
			// 1. 内迭代求解流场
			innerLoop->Solve();

			// 2. 计算广义气动力
			std::vector<double> generalizedAeroForces = this->ComputeGeneralizedAerodynamicForces();

			// 3. 求解广义结构运动方程
			if (outerStep > 0) {
				structuralDynamics->SolveGeneralizedEquation(generalizedAeroForces, physicalTimeStep);
			}

			// 4. 将广义位移转换为物理位移并更新网格
			const auto& generalizedDisplacement = structuralDynamics->GetGeneralizedDisplacement();
			this->ConvertGeneralizedToPhysicalDisplacement(generalizedDisplacement);

			// 5. 更新网格后重新计算网格相关量
			this->UpdateMeshMetrics();
		}
#else
		innerLoop->Solve();
#endif

		// 外循环物理场输出
		if (unsteady)
		{
			if (outerStep + 1 >= averagedStep)
			{
				resultProcess->CalculateMeanFields(computeAveragedStep);
				computeAveragedStep++;
			}
			
			resultProcess->MonitorPerStepOutLoop();
			CheckStatus(2401);

			if (currentTime >= totalTime || outerStep + 1 > outerLoopSteps)
			{
				resultProcess->SaveFinalResults();
				CheckStatus(2402);
				break;
			}
			else if (outerInterval > 0 && (outerStep + 1) % outerInterval == 0)
			{
				resultProcess->SaveIntervalResults();
				CheckStatus(2403);
			}
		}
	}
}

void OuterLoop::SetTimeStep()
{
	const Scalar &timeStep = flowPackageVector[0]->GetUnsteadyStatus().timeStep;
	for (int level = 0; level < flowPackageVector.size(); ++level)
		flowPackageVector[level]->UpdateCurrentTime(timeStep);
}

void OuterLoop::UpdateOldField(const int &outerStep)
{
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; ++level)
	{
		const int unsteadyFieldSize = (int)flowPackageVector[0]->GetFieldUnsteadyField().size();
		for (int i = unsteadyFieldSize - 1; i > 0; --i)
		{
			const int j = i - 1;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].density = *flowPackageVector[level]->GetFieldUnsteadyField()[j].density;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].velocity = *flowPackageVector[level]->GetFieldUnsteadyField()[j].velocity;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].pressure = *flowPackageVector[level]->GetFieldUnsteadyField()[j].pressure;
			for (int k = 0; k < flowPackageVector[level]->GetTurbulentStatus().nVariable; ++k)
				*flowPackageVector[level]->GetFieldUnsteadyField()[i].turbulence[k] = *flowPackageVector[level]->GetFieldUnsteadyField()[j].turbulence[k];
		}
	}
}

#if defined(_EnableFlutter_)
std::vector<double> OuterLoop::ComputeGeneralizedAerodynamicForces()
{
    std::vector<double> generalizedAeroForces;

    // 从收敛的流场中提取壁面压力和粘性力
    const auto& flowPackage = flowPackageVector[0];
    const auto& mesh = flowPackage->GetMesh();

    // 获取当前分区的物面摄动场（模态振型数据）
    const auto& wallPerturbationFields = flowPackage->GetWallPerturbationFields();
    if (wallPerturbationFields.empty()) {
        FatalError("未找到物面摄动场数据，请检查摄动场文件是否正确加载");
    }

    int numModes = wallPerturbationFields.size();
    generalizedAeroForces.resize(numModes, 0.0);

    if (GetMPIRank() == 0) {
        Print("计算广义气动力，模态数量: " + ToString(numModes));
    }

    // 遍历壁面边界，计算物理气动力并与物面摄动场做点积
    for (const auto& boundary : mesh->GetWallBoundaries()) {
        for (int faceIdx = 0; faceIdx < boundary.GetFaceNumber(); faceIdx++) {
            const auto& face = boundary.GetFace(faceIdx);

            // 计算压力力
            Scalar pressure = face.GetPressure();
            Vector normal = face.GetNormal();
            Scalar area = face.GetArea();
            Vector pressureForce = pressure * area * normal;

            // 计算粘性力（如果需要）
            Vector viscousForce = Vector0;
            if (flowPackage->GetFlowConfigure().GetModel().type != Turbulence::Model::INVISCID) {
                // 获取粘性应力
                viscousForce = face.GetViscousStress() * area;
            }

            // 总的物理气动力
            Vector totalAeroForce = pressureForce + viscousForce;

            // 与各个模态的物面摄动场做点积计算广义气动力
            for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
                // 获取当前壁面单元上的模态振型（物面摄动场）
                // 注意：这里需要根据面的节点来获取摄动场值
                Vector modeShape = Vector0;

                // 获取面的节点并计算平均摄动场值
                const auto& faceNodes = face.GetNodes();
                for (const auto& nodeID : faceNodes) {
                    modeShape += wallPerturbationFields[modeIdx]->GetValue(nodeID);
                }
                modeShape /= faceNodes.size(); // 平均值

                // 广义气动力 = 物理气动力 · 模态振型
                generalizedAeroForces[modeIdx] += totalAeroForce & modeShape;
            }
        }
    }

    // MPI并行环境下需要对广义气动力进行全局求和
#if defined(_BaseParallelMPI_)
    std::vector<double> globalGeneralizedForces(numModes, 0.0);
    MPI_Allreduce(generalizedAeroForces.data(), globalGeneralizedForces.data(),
                  numModes, MPI_DOUBLE, MPI_SUM, MPI_COMM_WORLD);
    generalizedAeroForces = globalGeneralizedForces;
#endif

    if (GetMPIRank() == 0) {
        Print("广义气动力计算完成:");
        for (int i = 0; i < numModes; i++) {
            Print("  模态 " + ToString(i+1) + ": " + ToString(generalizedAeroForces[i]));
        }
    }

    return generalizedAeroForces;
}

void OuterLoop::ConvertGeneralizedToPhysicalDisplacement(const std::vector<double>& generalizedDisp)
{
    // 获取网格和物面摄动场数据
    const auto& flowPackage = flowPackageVector[0];
    const auto& mesh = flowPackage->GetMesh();
    const auto& wallPerturbationFields = flowPackage->GetWallPerturbationFields();

    if (wallPerturbationFields.empty()) {
        FatalError("未找到物面摄动场数据，无法进行广义位移到物理位移的转换");
    }

    int numModes = wallPerturbationFields.size();
    if (generalizedDisp.size() != numModes) {
        FatalError("广义位移向量维度与模态数量不匹配");
    }

    if (GetMPIRank() == 0) {
        Print("将广义位移转换为物理位移，模态数量: " + ToString(numModes));
    }

    // 遍历所有壁面边界，计算物理位移
    std::vector<Vector> physicalDisplacements;

    for (const auto& boundary : mesh->GetWallBoundaries()) {
        for (int nodeIdx = 0; nodeIdx < boundary.GetNodeNumber(); nodeIdx++) {
            Vector totalDisplacement = Vector0;

            // 模态叠加：物理位移 = Σ(广义位移 * 物面摄动场)
            for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
                // 从物面摄动场中获取当前壁面节点的模态振型
                const auto& node = boundary.GetNode(nodeIdx);
                int nodeID = node.GetID();
                Vector modeShape = wallPerturbationFields[modeIdx]->GetValue(nodeID);
                totalDisplacement += generalizedDisp[modeIdx] * modeShape;
            }

            physicalDisplacements.push_back(totalDisplacement);
        }
    }

    // 使用物理位移更新网格
    motionManager->UpdateFlutterMeshWithDisplacements(physicalDisplacements);

    if (GetMPIRank() == 0) {
        Print("物理位移计算完成，更新了 " + ToString(physicalDisplacements.size()) + " 个壁面节点");
    }
}

void OuterLoop::UpdateMeshMetrics()
{
    // 更新网格几何量
    for (auto& flowPackage : flowPackageVector) {
        auto& mesh = flowPackage->GetMesh();
        
        // 重新计算单元体积
        mesh->UpdateElementVolumes();
        
        // 重新计算面积和法向量
        mesh->UpdateFaceMetrics();
        
        // 更新网格质量检查
        mesh->CheckMeshQuality();
    }
    
    // 更新边界条件中的几何相关项
    for (auto& timeScheme : timeSchemeVector) {
        timeScheme->UpdateBoundaryCondition();
    }
}

void OuterLoop::InitializeFlutterCalculation()
{
    const auto& flutterConfig = flowConfigure.GetFlutter();

    if (!flutterConfig.enableFlutter) {
        enableFlutter = false;
        return;
    }

    enableFlutter = true;
    physicalTimeStep = flowPackageVector[0]->GetUnsteadyStatus().physicalTimeStep;

    if (GetMPIRank() == 0) {
        Print("初始化颤振计算模块...");
        Print("强耦合模式: " + (flutterConfig.strongCoupling ? std::string("是") : std::string("否")));
    }

    // 读取并初始化摄动场数据
    LoadPerturbationFields();

    // 获取物面摄动场数据
    const auto& flowPackage = flowPackageVector[0];
    const auto& wallPerturbationFields = flowPackage->GetWallPerturbationFields();

    if (wallPerturbationFields.empty()) {
        FatalError("未找到物面摄动场数据，请检查摄动场文件是否正确加载");
    }

    // 从配置中获取固有频率（如果没有配置，使用默认值）
    std::vector<double> frequencies;
    if (!flutterConfig.naturalFrequencies.empty()) {
        frequencies = flutterConfig.naturalFrequencies;
    } else {
        // 使用默认频率值
        int numModes = wallPerturbationFields.size();
        frequencies.resize(numModes);
        for (int i = 0; i < numModes; i++) {
            frequencies[i] = 10.0 * (i + 1); // 默认频率：10, 20, 30... rad/s
        }
        if (GetMPIRank() == 0) {
            Print("警告：未配置固有频率，使用默认值");
        }
    }

    // 创建结构动力学求解器
    structuralDynamics = std::make_unique<StructuralDynamics>();
    structuralDynamics->Initialize(wallPerturbationFields, frequencies);

    // 设置阻尼参数
    if (!flutterConfig.dampingRatios.empty()) {
        structuralDynamics->SetDampingRatio(flutterConfig.dampingRatios);
    }

    // 设置Newmark参数
    structuralDynamics->SetNewmarkParameters(flutterConfig.newmarkBeta, flutterConfig.newmarkGamma);

    // 创建运动管理器
    motionManager = std::make_unique<MotionManager>(flowPackageVector);

    if (GetMPIRank() == 0) {
        Print("颤振计算模块初始化完成，模态数量: " + ToString(wallPerturbationFields.size()));
    }
}

void OuterLoop::LoadPerturbationFields()
{
    if (GetMPIRank() == 0) {
        Print("读取摄动场数据...");
    }

    // 获取摄动场配置
    const auto& perturbConfig = flowConfigure.GetPerturbation();
    if (!perturbConfig.FlutterFlag) {
        FatalError("摄动场配置中FlutterFlag未启用");
    }

    int perturbationNumber = perturbConfig.perturbationNumber;
    std::string perturbationPath = perturbConfig.perturbationPath;
    std::string perturbationName = perturbConfig.perturbationName;
    bool binaryFlag = perturbConfig.binaryFileFlag;

    // 读取当前分区的摄动场文件
    int mpiRank = GetMPIRank();
    std::string fileName = perturbationPath + perturbationName + "_" + ToString(mpiRank) + ".dat";

    std::fstream file;
    if (binaryFlag) {
        file.open(fileName, std::ios::in | std::ios::binary);
    } else {
        file.open(fileName, std::ios::in);
    }

    if (!file.is_open()) {
        FatalError("无法打开摄动场分区文件: " + fileName);
    }

    // 读取摄动场数据
    int numModes, numNodes;
    IO::Read(file, numModes, binaryFlag);
    IO::Read(file, numNodes, binaryFlag);

    if (numModes != perturbationNumber) {
        FatalError("摄动场文件中的模态数量与配置不匹配: " + ToString(numModes) + " vs " + ToString(perturbationNumber));
    }

    std::vector<std::vector<Vector>> perturbationData(numModes);
    for (int modeIdx = 0; modeIdx < numModes; modeIdx++) {
        perturbationData[modeIdx].resize(numNodes);
        IO::Read(file, perturbationData[modeIdx], binaryFlag, numNodes);
    }

    file.close();

    if (GetMPIRank() == 0) {
        Print("摄动场数据读取完成，模态数量: " + ToString(numModes) + "，节点数量: " + ToString(numNodes));
    }

    // 初始化FlowPackage中的摄动场
    flowPackageVector[0]->InitializePerturbationFields(perturbationData);
}
#endif
